# Database connection string
#DATABASE_URI=mongodb://127.0.0.1/your-database-name

# Or use a PG connection string
DATABASE_URI=postgresql://neondb_owner:<EMAIL>/ikia?sslmode=require&channel_binding=require

# Used to encrypt JWT tokens
PAYLOAD_SECRET=YOUR_SECRET_HERE

# Used to configure CORS, format links and more. No trailing slash
NEXT_PUBLIC_API_URL=http://localhost:3000

# Secret used to authenticate cron jobs
CRON_SECRET=YOUR_CRON_SECRET_HERE

# Used to validate preview requests
PREVIEW_SECRET=YOUR_SECRET_HERE

# Google Maps API Configuration
# Get your API key from: https://console.cloud.google.com/apis/credentials
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=AIzaSyDnjdphx-H56AMu3xdtsv4aSqJnXAgCDgc